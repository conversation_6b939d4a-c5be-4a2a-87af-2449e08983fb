#!/usr/bin/env python3
"""
Migration script to change the date column in pre_bl_ocr table 
from TIMESTAMP WITH TIME ZONE to TIMESTAMP WITHOUT TIME ZONE.

This will remove the timezone information (+00) from stored dates.
"""

import sys
import os
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(project_root))

# Set environment before importing database connection
def set_environment(env_name):
    """Set the environment for database connection."""
    if env_name == 'prod':
        os.environ['ENV_FILE'] = '.env.prod'
    elif env_name == 'preprod':
        os.environ['ENV_FILE'] = '.env.preprod'
    else:
        os.environ['ENV_FILE'] = '.env.local'

from sqlalchemy import text
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def migrate_date_column(env_name='local'):
    """
    Migrate the date column from TIMESTAMP WITH TIME ZONE to TIMESTAMP WITHOUT TIME ZONE.
    This removes the timezone information from the stored dates.
    """
    try:
        # Import after environment is set
        from src.app.database.connection import get_session

        with get_session() as session:
            logger.info(f"Starting migration of date column for {env_name} environment...")
            
            # Step 1: Check current column type
            check_column_sql = """
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'pre_bl_ocr' AND column_name = 'date';
            """
            
            result = session.execute(text(check_column_sql)).fetchone()
            if result:
                logger.info(f"Current column info: {result}")
            else:
                logger.error("Column 'date' not found in pre_bl_ocr table")
                return False
            
            # Step 2: Alter the column type
            # This will convert existing timestamps to local time without timezone
            alter_column_sql = """
            ALTER TABLE pre_bl_ocr 
            ALTER COLUMN date TYPE TIMESTAMP WITHOUT TIME ZONE 
            USING date AT TIME ZONE 'UTC';
            """
            
            logger.info("Executing column type change...")
            session.execute(text(alter_column_sql))
            
            # Step 3: Update the default value
            alter_default_sql = """
            ALTER TABLE pre_bl_ocr 
            ALTER COLUMN date SET DEFAULT NOW();
            """
            
            logger.info("Updating default value...")
            session.execute(text(alter_default_sql))
            
            # Step 4: Verify the change
            verify_sql = """
            SELECT column_name, data_type, column_default
            FROM information_schema.columns 
            WHERE table_name = 'pre_bl_ocr' AND column_name = 'date';
            """
            
            result = session.execute(text(verify_sql)).fetchone()
            logger.info(f"Updated column info: {result}")
            
            # Step 5: Show sample data to verify format
            sample_sql = """
            SELECT "ID_BL", date, status
            FROM pre_bl_ocr
            ORDER BY "ID_BL" DESC
            LIMIT 5;
            """
            
            sample_results = session.execute(text(sample_sql)).fetchall()
            logger.info("Sample data after migration:")
            for row in sample_results:
                logger.info(f"  ID_BL: {row[0]}, date: {row[1]}, status: {row[2]}")
            
            session.commit()
            logger.info("✅ Migration completed successfully!")
            return True
            
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        return False

def rollback_migration(env_name='local'):
    """
    Rollback the migration by changing the column back to TIMESTAMP WITH TIME ZONE.
    """
    try:
        # Import after environment is set
        from src.app.database.connection import get_session

        with get_session() as session:
            logger.info(f"Starting rollback of date column migration for {env_name} environment...")
            
            # Rollback: Change back to TIMESTAMP WITH TIME ZONE
            rollback_sql = """
            ALTER TABLE pre_bl_ocr 
            ALTER COLUMN date TYPE TIMESTAMP WITH TIME ZONE 
            USING date AT TIME ZONE 'UTC';
            """
            
            logger.info("Executing rollback...")
            session.execute(text(rollback_sql))
            
            # Update default value back
            rollback_default_sql = """
            ALTER TABLE pre_bl_ocr 
            ALTER COLUMN date SET DEFAULT NOW();
            """
            
            session.execute(text(rollback_default_sql))
            session.commit()
            
            logger.info("✅ Rollback completed successfully!")
            return True
            
    except Exception as e:
        logger.error(f"❌ Rollback failed: {e}")
        return False

if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description='Migrate date column in pre_bl_ocr table')
    parser.add_argument('--rollback', action='store_true',
                       help='Rollback the migration (change back to TIMESTAMP WITH TIME ZONE)')
    parser.add_argument('--env', choices=['local', 'preprod', 'prod'], default='local',
                       help='Environment to run migration on (default: local)')

    args = parser.parse_args()

    # Set environment before running migration
    set_environment(args.env)

    logger.info(f"Running migration on {args.env} environment")

    if args.rollback:
        success = rollback_migration(args.env)
    else:
        success = migrate_date_column(args.env)

    sys.exit(0 if success else 1)
