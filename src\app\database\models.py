"""
SQLAlchemy models for PostgreSQL database.
This module defines the database models that match the existing SQLite schema.
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func
from datetime import datetime
import json

Base = declarative_base()


class PreBlOcr(Base):
    """
    Model for pre_bl_ocr table - stores OCR processing results for delivery notes.
    This model matches the existing SQLite schema structure.
    """
    __tablename__ = 'pre_bl_ocr'

    # Primary key - auto-incrementing ID
    ID_BL = Column(Integer, primary_key=True, autoincrement=True)
    
    # JSON content storing the complete OCR results
    Content = Column(JSON, nullable=True)
    
    # User and tenant information for multi-tenant support
    ID_USER = Column(String(255), nullable=True)
    ID_TENANT = Column(String(255), nullable=True)
    CODE_TENANT = Column(String(255), nullable=True)
    
    # Processing status with default value
    status = Column(String(50), nullable=False, default='EN_ATTENTE')
    
    # Timestamp with automatic current timestamp
    date = Column(DateTime(timezone=True), nullable=False, server_default=func.now())
    
    # Original document references
    id_BL_origine = Column(String(255), nullable=True)
    date_BL_origine = Column(String(255), nullable=True)
    
    # Supplier information
    supplier_name = Column(String(255), nullable=True)
    supplier_id = Column(String(255), nullable=True)
    
    # Unique processing identifier
    random_id = Column(String(255), nullable=True)

    # Platform source identifier (pharmalien or winpluspharma)
    src_app = Column(String(50), nullable=False, default='winpluspharma')

    def __repr__(self):
        return f"<PreBlOcr(ID_BL={self.ID_BL}, status='{self.status}', supplier_name='{self.supplier_name}', src_app='{self.src_app}')>"

    def to_dict(self):
        """
        Convert the model instance to a dictionary.
        This method helps with serialization and compatibility with existing code.
        """
        # Format date as "yyyy-MM-dd HH:mm:ss" without timezone and microseconds
        formatted_date = None
        if self.date:
            # Convert to local time and format as requested
            formatted_date = self.date.strftime("%Y-%m-%d %H:%M:%S")

        return {
            'ID_BL': self.ID_BL,
            'Content': self.Content if isinstance(self.Content, (dict, list)) else json.loads(self.Content) if self.Content else None,
            'ID_USER': self.ID_USER,
            'status': self.status,
            'ID_TENANT': self.ID_TENANT,
            'CODE_TENANT': self.CODE_TENANT,
            'date': formatted_date,
            'id_BL_origine': self.id_BL_origine,
            'date_BL_origine': self.date_BL_origine,
            'supplier_name': self.supplier_name,
            'supplier_id': self.supplier_id,
            'random_id': self.random_id,
            'src_app': self.src_app  # Added missing src_app field
        }

    @classmethod
    def from_dict(cls, data):
        """
        Create a model instance from a dictionary.
        This method helps with data migration and compatibility.
        """
        # Handle Content field - ensure it's properly formatted for JSON storage
        content = data.get('Content')
        if isinstance(content, str):
            try:
                content = json.loads(content)
            except json.JSONDecodeError:
                content = None
        
        # Handle date field
        date_value = data.get('date')
        if isinstance(date_value, str):
            try:
                # Try to parse the new format "yyyy-MM-dd HH:mm:ss" first
                date_value = datetime.strptime(date_value, "%Y-%m-%d %H:%M:%S")
            except ValueError:
                try:
                    # Fallback to ISO format for backward compatibility
                    date_value = datetime.fromisoformat(date_value.replace('Z', '+00:00'))
                except ValueError:
                    date_value = datetime.now()
        elif date_value is None:
            date_value = datetime.now()
        
        return cls(
            ID_BL=data.get('ID_BL'),
            Content=content,
            ID_USER=data.get('ID_USER'),
            status=data.get('status', 'EN_ATTENTE'),
            ID_TENANT=data.get('ID_TENANT'),
            CODE_TENANT=data.get('CODE_TENANT'),
            date=date_value,
            id_BL_origine=data.get('id_BL_origine'),
            date_BL_origine=data.get('date_BL_origine'),
            supplier_name=data.get('supplier_name'),
            supplier_id=data.get('supplier_id'),
            random_id=data.get('random_id'),
            src_app=data.get('src_app', 'winpluspharma')  # Added missing src_app field with default
        )


# Additional models can be added here as the system grows
# For example, if you want to add separate tables for:
# - Users management
# - Suppliers information
# - Processing logs
# - Configuration settings

class ProcessingLog(Base):
    """
    Optional model for tracking processing operations and errors.
    This can be useful for monitoring and debugging.
    """
    __tablename__ = 'processing_logs'

    id = Column(Integer, primary_key=True, autoincrement=True)
    bl_id = Column(Integer, nullable=True)  # Reference to PreBlOcr.ID_BL
    user_id = Column(String(255), nullable=True)
    tenant_id = Column(String(255), nullable=True)
    operation = Column(String(100), nullable=False)  # 'create', 'update', 'delete', 'process'
    status = Column(String(50), nullable=False)  # 'success', 'error', 'warning'
    message = Column(Text, nullable=True)
    error_details = Column(JSON, nullable=True)
    timestamp = Column(DateTime(timezone=True), nullable=False, server_default=func.now())
    processing_time_ms = Column(Integer, nullable=True)

    def __repr__(self):
        return f"<ProcessingLog(id={self.id}, operation='{self.operation}', status='{self.status}')>"


class SupplierConfig(Base):
    """
    Optional model for storing supplier-specific configurations.
    This can help manage different OCR settings per supplier.
    """
    __tablename__ = 'supplier_configs'

    id = Column(Integer, primary_key=True, autoincrement=True)
    supplier_name = Column(String(255), nullable=False, unique=True)
    supplier_id = Column(String(255), nullable=True)
    config_data = Column(JSON, nullable=True)  # Store OCR settings, thresholds, etc.
    is_active = Column(String(10), nullable=False, default='true')
    created_at = Column(DateTime(timezone=True), nullable=False, server_default=func.now())
    updated_at = Column(DateTime(timezone=True), nullable=False, server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<SupplierConfig(supplier_name='{self.supplier_name}', is_active='{self.is_active}')>"
