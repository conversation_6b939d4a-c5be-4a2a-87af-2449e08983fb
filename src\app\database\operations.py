"""
PostgreSQL database operations.
This module provides operations for the PostgreSQL database.
"""

import logging
from typing import List, Dict, Optional, Any
from datetime import datetime

from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import and_, desc, or_

from src.app.database.connection import get_session, db_manager
from src.app.database.models import PreBlOcr

logger = logging.getLogger(__name__)


class DatabaseOperations:
    """
    Handles operations for the PostgreSQL database.
    """
    
    def __init__(self):
        pass
    
    def save_response_to_db(self, responses: List[Dict], id_user: str,
                          tenant_code: str, tenant_id: str, random_id: str, src_app: str = 'winpluspharma') -> int:
        """
        Save OCR response data to PostgreSQL database.
        Returns the ID of the saved record.
        """
        # Extract only the 'data' part from each response
        data_to_save = [response['data'] for response in responses 
                       if response.get('success') and response.get('data')]
        
        # Prepare the record data
        record_data = {
            'Content': data_to_save,
            'ID_USER': id_user,
            'ID_TENANT': tenant_id,
            'CODE_TENANT': tenant_code,
            'random_id': random_id,
            'status': 'EN_ATTENTE',
            'date': datetime.now().replace(microsecond=0),
            'src_app': src_app
        }

        # log data:
        logger.info(f"Saving to PostgreSQL: {record_data}")
        
        try:
            with get_session() as session:
                record = PreBlOcr.from_dict(record_data)
                session.add(record)
                session.flush()  # Get the ID without committing
                record_id = record.ID_BL
                session.commit()
                logger.info(f"Saved to PostgreSQL with ID: {record_id}")
                return record_id
        except Exception as e:
            logger.error(f"Failed to save to database: {e}")
            raise
    
    def get_all_pre_bl_ocr(self, user_id: str, tenant_id: str, src_app: str = None, status_filter: str = None) -> List[Dict]:
        """Get all pre_bl_ocr records for a user and tenant with optional status filtering."""
        try:
            with get_session() as session:
                # Build filter conditions
                filter_conditions = [
                    PreBlOcr.ID_USER == user_id,
                    PreBlOcr.ID_TENANT == tenant_id
                ]

                # Handle status filtering
                if status_filter == 'N':
                    # "N" means all statuses except EN_ATTENTE
                    filter_conditions.append(PreBlOcr.status.in_(['EN_COURS', 'VALIDER']))
                elif status_filter == 'EN_COURS':
                    filter_conditions.append(PreBlOcr.status == 'EN_COURS')
                elif status_filter == 'VALIDER':
                    filter_conditions.append(PreBlOcr.status == 'VALIDER')
                else:
                    # Default behavior: exclude EN_ATTENTE (same as "N")
                    filter_conditions.append(PreBlOcr.status.in_(['EN_COURS', 'VALIDER']))

                # Add src_app filter if provided
                if src_app:
                    filter_conditions.append(PreBlOcr.src_app == src_app)

                records = session.query(PreBlOcr).filter(
                    and_(*filter_conditions)
                ).order_by(desc(PreBlOcr.date)).all()
                
                return [record.to_dict() for record in records]
        except Exception as e:
            logger.error(f"Failed to get records: {e}")
            raise
    
    def get_pre_bl_ocr_by_id(self, id_bl: int, user_id: str, tenant_id: str, src_app: str = None) -> Optional[Dict]:
        """Get a specific pre_bl_ocr record by ID."""
        try:
            with get_session() as session:
                # Build filter conditions
                filter_conditions = [
                    PreBlOcr.ID_BL == id_bl,
                    PreBlOcr.status.in_(['EN_COURS', 'VALIDER']),
                    PreBlOcr.ID_USER == user_id,
                    PreBlOcr.ID_TENANT == tenant_id
                ]

                # Add src_app filter if provided
                if src_app:
                    filter_conditions.append(PreBlOcr.src_app == src_app)

                record = session.query(PreBlOcr).filter(
                    and_(*filter_conditions)
                ).first()
                
                return record.to_dict() if record else None
        except Exception as e:
            logger.error(f"Failed to get record by ID: {e}")
            return None
    
    def get_pre_bl_ocr_by_random_id(self, random_id: str, user_id: str, tenant_id: str) -> Optional[Dict]:
        """Get a specific pre_bl_ocr record by random_id."""
        try:
            with get_session() as session:
                record = session.query(PreBlOcr).filter(
                    and_(
                        PreBlOcr.random_id == random_id,
                        PreBlOcr.status.in_(['EN_COURS', 'VALIDER']),
                        PreBlOcr.ID_USER == user_id,
                        PreBlOcr.ID_TENANT == tenant_id
                    )
                ).first()
                
                return record.to_dict() if record else None
        except Exception as e:
            logger.error(f"Failed to get record by random_id: {e}")
            return None
    
    def update_bl_status(self, bl_id: int, new_status: str, id_BL_origine: str, 
                        date_BL_origine: str, supplier_name: str, supplier_id: str) -> bool:
        """Update BL status in PostgreSQL database."""
        try:
            with get_session() as session:
                record = session.query(PreBlOcr).filter(PreBlOcr.ID_BL == bl_id).first()
                if record:
                    record.status = new_status
                    record.id_BL_origine = id_BL_origine
                    record.date_BL_origine = date_BL_origine
                    record.supplier_name = supplier_name
                    record.supplier_id = supplier_id
                    session.commit()
                    logger.info(f"Updated BL status for ID: {bl_id}")
                    return True
                return False
        except Exception as e:
            logger.error(f"Failed to update BL status: {e}")
            return False
    
    def update_bl_status_valider(self, bl_id: int, user_id: str, tenant_id: str) -> bool:
        """Update BL status to 'VALIDER' in PostgreSQL database."""
        try:
            with get_session() as session:
                record = session.query(PreBlOcr).filter(
                    and_(
                        PreBlOcr.ID_BL == bl_id,
                        PreBlOcr.ID_USER == user_id,
                        PreBlOcr.ID_TENANT == tenant_id
                    )
                ).first()
                if record:
                    record.status = 'VALIDER'
                    session.commit()
                    logger.info(f"Updated BL status to VALIDER for ID: {bl_id}")
                    return True
                return False
        except Exception as e:
            logger.error(f"Failed to update BL status to VALIDER: {e}")
            return False
    
    def get_database_health(self) -> Dict[str, Any]:
        """Get health status of the PostgreSQL database."""
        return db_manager.health_check()

    def get_bl_scannes_with_criteria(self, criteria: Dict[str, Any], id_hash_user: str, src_app: str = None) -> List[Dict]:
        """
        Get pre_bl_ocr records with dynamic filtering criteria for windoc endpoints.

        Args:
            criteria: Dictionary containing filtering criteria
                - NumeroBL: Filter by id_BL_origine (exact match)
                - blId: Filter by id_BL_origine (exact match)
                - numeroBlStr: Filter by id_BL_origine (exact match)
                - dateBl: Filter by date_BL_origine (exact match)
                - dateBlDebut: Start date for date range filtering
                - dateBlFin: End date for date range filtering
                - statutBl: Status filter ("N" for all except EN_ATTENTE, "EN_COURS", "VALIDER")
            id_hash_user: Hash user ID for authentication (maps to ID_USER in table)
            src_app: Platform source filter

        Returns:
            List of matching records
        """
        try:
            with get_session() as session:
                # Base filter conditions - filter by ID_USER
                filter_conditions = [
                    PreBlOcr.ID_USER == id_hash_user
                ]

                # Handle status filtering based on statutBl parameter
                statut_bl = criteria.get('statutBl')
                if statut_bl == 'N':
                    # "N" means all statuses except EN_ATTENTE
                    filter_conditions.append(PreBlOcr.status.in_(['EN_COURS', 'VALIDER']))
                elif statut_bl == 'EN_COURS':
                    # Specific status filter for EN_COURS
                    filter_conditions.append(PreBlOcr.status == 'EN_COURS')
                elif statut_bl == 'VALIDER':
                    # Specific status filter for VALIDER
                    filter_conditions.append(PreBlOcr.status == 'VALIDER')
                else:
                    # Default behavior: exclude EN_ATTENTE (same as "N")
                    filter_conditions.append(PreBlOcr.status.in_(['EN_COURS', 'VALIDER']))

                # # Add src_app filter if provided
                # if src_app:
                #     filter_conditions.append(PreBlOcr.src_app == src_app)

                # Dynamic criteria filtering
                if criteria.get('NumeroBL'):
                    # Filter by id_BL_origine (original BL number)
                    filter_conditions.append(PreBlOcr.id_BL_origine == criteria['NumeroBL'])

                if criteria.get('blId'):
                    # Filter by id_BL_origine using blId field
                    filter_conditions.append(PreBlOcr.id_BL_origine == criteria['blId'])

                if criteria.get('numeroBlStr'):
                    # Filter by id_BL_origine using numeroBlStr field
                    filter_conditions.append(PreBlOcr.id_BL_origine == criteria['numeroBlStr'])

                if criteria.get('dateBl'):
                    filter_conditions.append(PreBlOcr.date_BL_origine == criteria['dateBl'])

                # Date range filtering
                date_conditions = []
                if criteria.get('dateBlDebut') and criteria.get('dateBlFin'):
                    # Both dates provided - filter between dates
                    date_conditions.append(
                        and_(
                            PreBlOcr.date_BL_origine >= criteria['dateBlDebut'],
                            PreBlOcr.date_BL_origine <= criteria['dateBlFin']
                        )
                    )
                elif criteria.get('dateBlDebut'):
                    # Only start date - filter greater than or equal
                    date_conditions.append(PreBlOcr.date_BL_origine >= criteria['dateBlDebut'])
                elif criteria.get('dateBlFin'):
                    # Only end date - filter less than or equal
                    date_conditions.append(PreBlOcr.date_BL_origine <= criteria['dateBlFin'])

                if date_conditions:
                    filter_conditions.extend(date_conditions)

                # Execute query
                records = session.query(PreBlOcr).filter(
                    and_(*filter_conditions)
                ).order_by(desc(PreBlOcr.date)).all()

                logger.info(f"Found {len(records)} records matching criteria for user {id_hash_user}")
                return [record.to_dict() for record in records]

        except Exception as e:
            logger.error(f"Failed to get records with criteria: {e}")
            raise

    def get_bl_scanne_by_id_with_criteria(self, id_bl: int, id_hash_user: str, src_app: str = None) -> Optional[Dict]:
        """
        Get a specific pre_bl_ocr record by ID for windoc endpoints.

        Args:
            id_bl: BL ID to retrieve
            id_hash_user: Hash user ID for authentication (maps to ID_USER in table)
            src_app: Platform source filter

        Returns:
            Record dictionary if found, None otherwise
        """
        try:
            with get_session() as session:
                # Base filter conditions - only EN_COURS status and filter by ID_USER
                filter_conditions = [
                    PreBlOcr.ID_BL == id_bl,
                    PreBlOcr.status == 'EN_COURS',
                    PreBlOcr.ID_USER == id_hash_user
                ]

                # Add src_app filter if provided
                if src_app:
                    filter_conditions.append(PreBlOcr.src_app == src_app)

                record = session.query(PreBlOcr).filter(
                    and_(*filter_conditions)
                ).first()

                if record:
                    logger.info(f"Retrieved record with ID: {id_bl} for user {id_hash_user}")
                    return record.to_dict()
                else:
                    logger.info(f"No record found with ID: {id_bl} for user {id_hash_user}")
                    return None

        except Exception as e:
            logger.error(f"Failed to get record by ID: {e}")
            return None

    def update_bl_status_valider_windoc(self, criteria: Dict[str, Any], id_hash_user: str) -> int:
        """
        Update BL status to 'VALIDER' for windoc endpoints using criteria.

        Args:
            criteria: Dictionary containing filtering criteria to identify the BL
                - blId: BL ID to update (required)
                - Other criteria can be used for additional validation
            id_hash_user: Hash user ID for authentication (maps to ID_USER in table)

        Returns:
            1 if update successful, 0 if failed
        """
        try:
            with get_session() as session:
                # Extract BL ID from criteria
                if not criteria.get('blId'):
                    logger.error("blId is required in criteria for status update")
                    return 0

                try:
                    bl_id = int(criteria['blId'])
                except ValueError:
                    logger.error("Invalid blId format - must be a number")
                    return 0

                # Base filter conditions - only EN_COURS status and filter by ID_USER
                filter_conditions = [
                    PreBlOcr.ID_BL == bl_id,
                    PreBlOcr.status == 'EN_COURS',  # Only update records that are currently EN_COURS
                    PreBlOcr.ID_USER == id_hash_user
                ]

                record = session.query(PreBlOcr).filter(
                    and_(*filter_conditions)
                ).first()

                if record:
                    record.status = 'VALIDER'
                    session.commit()
                    logger.info(f"Updated BL status to VALIDER for ID: {bl_id} by user {id_hash_user}")
                    return 1
                else:
                    logger.info(f"No EN_COURS record found with ID: {bl_id} for user {id_hash_user}")
                    return 0

        except Exception as e:
            logger.error(f"Failed to update BL status to VALIDER: {e}")
            return 0


# Global instance for easy access
db_ops = DatabaseOperations()
