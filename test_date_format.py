#!/usr/bin/env python3
"""
Test script to verify the new date format in PreBlOcr model.
This script tests the date formatting changes to ensure dates are stored and retrieved
in the format "yyyy-MM-dd HH:mm:ss" instead of the previous format with timezone and microseconds.
"""

import sys
import os
from datetime import datetime

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from app.database.models import PreBlOcr

def test_date_formatting():
    """Test the date formatting in PreBlOcr model."""
    print("Testing PreBlOcr date formatting...")
    
    # Test 1: Create a PreBlOcr instance with current datetime
    print("\n1. Testing to_dict() method with current datetime:")
    
    # Create a sample PreBlOcr instance
    sample_data = {
        'ID_BL': 1,
        'Content': {'test': 'data'},
        'ID_USER': 'test_user',
        'status': 'EN_ATTENTE',
        'ID_TENANT': 'test_tenant',
        'CODE_TENANT': 'TEST',
        'date': datetime.now(),
        'id_BL_origine': 'BL001',
        'date_BL_origine': '01/01/2025',
        'supplier_name': 'Test Supplier',
        'supplier_id': 'SUP001',
        'random_id': 'random123',
        'src_app': 'winpluspharma'
    }
    
    # Create PreBlOcr instance using from_dict
    pre_bl_ocr = PreBlOcr.from_dict(sample_data)
    
    # Convert to dictionary and check date format
    result_dict = pre_bl_ocr.to_dict()
    print(f"Original datetime: {sample_data['date']}")
    print(f"Formatted date: {result_dict['date']}")
    print(f"Expected format: yyyy-MM-dd HH:mm:ss")
    
    # Verify the format
    try:
        parsed_date = datetime.strptime(result_dict['date'], "%Y-%m-%d %H:%M:%S")
        print("✅ Date format is correct!")
    except ValueError as e:
        print(f"❌ Date format is incorrect: {e}")
        return False
    
    # Test 2: Test from_dict with the new format
    print("\n2. Testing from_dict() method with new format:")
    
    test_date_str = "2025-07-02 15:30:45"
    test_data = {
        'ID_BL': 2,
        'Content': {'test': 'data2'},
        'ID_USER': 'test_user2',
        'status': 'VALIDER',
        'ID_TENANT': 'test_tenant2',
        'CODE_TENANT': 'TEST2',
        'date': test_date_str,
        'id_BL_origine': 'BL002',
        'date_BL_origine': '02/01/2025',
        'supplier_name': 'Test Supplier 2',
        'supplier_id': 'SUP002',
        'random_id': 'random456',
        'src_app': 'pharmalien'
    }
    
    try:
        pre_bl_ocr2 = PreBlOcr.from_dict(test_data)
        result_dict2 = pre_bl_ocr2.to_dict()
        print(f"Input date string: {test_date_str}")
        print(f"Output date string: {result_dict2['date']}")
        
        if result_dict2['date'] == test_date_str:
            print("✅ Round-trip date conversion is correct!")
        else:
            print(f"❌ Round-trip date conversion failed!")
            return False
            
    except Exception as e:
        print(f"❌ Error in from_dict with new format: {e}")
        return False
    
    # Test 3: Test backward compatibility with old format
    print("\n3. Testing backward compatibility with old ISO format:")
    
    old_format_date = "2025-07-02T11:10:52.214779+00:00"
    test_data_old = {
        'ID_BL': 3,
        'Content': {'test': 'data3'},
        'ID_USER': 'test_user3',
        'status': 'EN_COURS',
        'ID_TENANT': 'test_tenant3',
        'CODE_TENANT': 'TEST3',
        'date': old_format_date,
        'id_BL_origine': 'BL003',
        'date_BL_origine': '03/01/2025',
        'supplier_name': 'Test Supplier 3',
        'supplier_id': 'SUP003',
        'random_id': 'random789',
        'src_app': 'winpluspharma'
    }
    
    try:
        pre_bl_ocr3 = PreBlOcr.from_dict(test_data_old)
        result_dict3 = pre_bl_ocr3.to_dict()
        print(f"Input old format: {old_format_date}")
        print(f"Output new format: {result_dict3['date']}")
        
        # Verify the output is in the new format
        try:
            datetime.strptime(result_dict3['date'], "%Y-%m-%d %H:%M:%S")
            print("✅ Backward compatibility works correctly!")
        except ValueError:
            print("❌ Backward compatibility failed - output format is incorrect!")
            return False
            
    except Exception as e:
        print(f"❌ Error in backward compatibility test: {e}")
        return False
    
    print("\n🎉 All tests passed! Date formatting is working correctly.")
    return True

if __name__ == "__main__":
    success = test_date_formatting()
    sys.exit(0 if success else 1)
